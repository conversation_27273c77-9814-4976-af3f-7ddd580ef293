import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

enum AppTheme {
  light,
  dark,
  blue,
  purple,
}

class AppThemeData {
  static ThemeData getThemeData(AppTheme theme) {
    switch (theme) {
      case AppTheme.light:
        return ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF4285F4),
            brightness: Brightness.light,
          ),
          useMaterial3: true,
          textTheme: GoogleFonts.poppinsTextTheme(),
          cardTheme: CardTheme(
            elevation: 6,
            shadowColor: Colors.black26,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          appBarTheme: const AppBarTheme(
            elevation: 0,
            centerTitle: false,
            titleTextStyle: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
        );
      case AppTheme.dark:
        return ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF4285F4),
            brightness: Brightness.dark,
          ),
          useMaterial3: true,
          textTheme: GoogleFonts.poppinsTextTheme(ThemeData.dark().textTheme),
          cardTheme: CardTheme(
            elevation: 6,
            shadowColor: Colors.black45,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          appBarTheme: const AppBarTheme(
            elevation: 0,
            centerTitle: false,
            titleTextStyle: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
        );
      case AppTheme.blue:
        return ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF2196F3),
            brightness: Brightness.light,
          ),
          primaryColor: Colors.blue[700],
          scaffoldBackgroundColor: Colors.blue[50],
          useMaterial3: true,
          textTheme: GoogleFonts.montserratTextTheme(),
          cardTheme: CardTheme(
            elevation: 6,
            shadowColor: Colors.blue.withOpacity(0.3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          appBarTheme: AppBarTheme(
            backgroundColor: Colors.blue[700],
            foregroundColor: Colors.white,
            elevation: 0,
            centerTitle: false,
            titleTextStyle: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
        );
      case AppTheme.purple:
        return ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF9C27B0),
            brightness: Brightness.dark,
          ),
          primaryColor: Colors.purple[700],
          scaffoldBackgroundColor: const Color(0xFF1E1E2E),
          useMaterial3: true,
          textTheme: GoogleFonts.rubikTextTheme(ThemeData.dark().textTheme),
          cardTheme: CardTheme(
            elevation: 6,
            shadowColor: Colors.purple.withOpacity(0.3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          appBarTheme: AppBarTheme(
            backgroundColor: Colors.purple[900],
            foregroundColor: Colors.white,
            elevation: 0,
            centerTitle: false,
            titleTextStyle: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
        );
    }
  }
} 